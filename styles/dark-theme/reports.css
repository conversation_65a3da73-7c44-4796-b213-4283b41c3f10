/* Reports Page - Dark Theme */

.reports-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-color: #1a1a1a;
    overflow: visible;
}

/* Controls bar */
.reports-controls {
    padding: 12px 18px;
    background-color: #252525;
    border-bottom: 1px solid #333;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.controls-row {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    align-items: center;
}

.control-group {
    display: flex;
    align-items: center;
    gap: 6px;
}

.control-group label {
    color: #ffffff;
    font-size: 14px;
    white-space: nowrap;
}

.control-group input[type="datetime-local"],
.control-group select {
    background-color: #2c2c2c;
    border: none;
    border-radius: 4px;
    color: #e0e0e0;
    padding: 6px 10px;
    font-size: 14px;
    height: 32px;
}

.generate-btn {
    background: transparent;
    border: 1px solid #555;
    color: #aaa;
    border-radius: 4px;
    padding: 6px 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.generate-btn:hover {
    background-color: #444;
    color: #fff;
}

/* Vertical separator */
.vertical-separator {
    width: 1px;
    height: 32px;
    background-color: #555;
    margin: 0 8px;
    flex-shrink: 0;
}

/* Content */
.reports-content {
    flex: 1;
    overflow-y: auto;
    padding: 0 18px 18px;
    background-color: #1a1a1a;
    margin-bottom: 20px;
    max-height: calc(100vh - 200px);
}

.reports-loading,
.reports-empty,
.reports-error,
.reports-placeholder {
    text-align: center;
    color: #aaaaaa;
    font-size: 16px;
    padding: 40px 20px;
}

.reports-error {
    color: #d41d28;
}

/* Table */
.report-table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    font-size: 14px;
}

.report-table thead {
    position: sticky;
    top: -1px;
    background-color: #252525;
    z-index: 5;
    padding-top: 1px;
}

.report-table th,
.report-table td {
    padding: 10px 14px;
    text-align: left;
    vertical-align: middle;
    border: 1px solid rgba(255, 255, 255, 0.15);
    color: #e0e0e0;
}

.report-table tbody tr:nth-child(even) {
    background-color: rgba(255, 255, 255, 0.04);
}

/* Status colours */
.ok { color: #cde06b; }
.warning { color: #ffa500; }
.critical { color: #d41d28; }
.unknown { color: #64748b; }

/* Report Summary */
.report-summary {
    background: linear-gradient(135deg, #2a2a2a 0%, #1f1f1f 100%);
    border: 1px solid #444;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 25px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
}

.summary-title {
    margin: 0 0 15px 0;
    font-size: 20px;
    font-weight: 600;
    color: #e0e0e0;
    text-align: center;
}

.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.summary-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #252525;
    border-radius: 6px;
    border: 1px solid #444;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
    transition: all 0.2s ease;
}

.summary-stat:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(0,0,0,0.3);
}

.summary-stat.warning {
    border-left: 4px solid #ffa500;
    background: linear-gradient(135deg, #2d2d1a 0%, #25251a 100%);
}

.summary-stat.critical {
    border-left: 4px solid #d41d28;
    background: linear-gradient(135deg, #2d1a1a 0%, #251a1a 100%);
}

.summary-stat.unknown {
    border-left: 4px solid #64748b;
    background: linear-gradient(135deg, #2a2a2a 0%, #1f1f1f 100%);
}

.stat-label {
    font-size: 14px;
    font-weight: 500;
    color: #aaa;
}

.stat-value {
    font-size: 16px;
    font-weight: 600;
    color: #e0e0e0;
}

.summary-stat.warning .stat-value {
    color: #ffa500;
}

.summary-stat.critical .stat-value {
    color: #d41d28;
}

.summary-stat.unknown .stat-value {
    color: #64748b;
}

/* Pie chart styles */
.summary-pie-chart {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    margin-top: 15px;
}

.pie-chart-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
}

.pie-chart-legend {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 10px;
}

.pie-legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.pie-legend-color {
    width: 16px;
    height: 16px;
    border-radius: 3px;
    border: 1px solid #444;
}

.pie-legend-label {
    color: #e0e0e0;
    font-weight: 500;
}

/* Charts container */
.charts-container {
    display: flex;
    gap: 30px;
    margin-top: 20px;
    flex-wrap: wrap;
}

.chart-section {
    flex: 1;
    min-width: 300px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.chart-title {
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: 500;
    color: #e0e0e0;
    text-align: center;
}

/* Marimekko chart styles */
.summary-marimekko-chart {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    width: 100%;
}

.marimekko-chart-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
}

.marimekko-empty {
    color: #aaa;
    font-style: italic;
    text-align: center;
    padding: 20px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .charts-container {
        flex-direction: column;
        gap: 20px;
    }

    .chart-section {
        min-width: unset;
    }
}

/* Hostgroup title */
.report-title {
    margin: 25px 0 10px;
    font-size: 18px;
    font-weight: 600;
    color: #ffffff;
}

.report-title:first-of-type {
    margin-top: 0; /* no gap at very top */
}

.reports-content > .report-title:first-child {
    margin-top: 0;
}

/* Subtle background for status cells */
.report-table td.ok { background-color: rgba(205,224,107,0.12); }
.report-table td.critical { background-color: rgba(212,29,40,0.12); }
.report-table td.warning { background-color: rgba(255,165,0,0.12); }
.report-table td.unknown { background-color: rgba(100,116,139,0.12); }

.hostgroup-table { margin-bottom: 35px; }

/* Status filter buttons */
.status-row { justify-content: flex-start; }

.reports-status-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 6px 10px;
    align-items: center;
}

.reports-status-filter {
    cursor: pointer;
    border: none;
    border-radius: 4px;
    padding: 4px 10px;
    font-size: 12px;
    font-weight: 600;
    transition: all 0.2s ease;
    opacity: 0.8;
}

.reports-status-filter.ok { background:#4CAF50; color:#fff; }
.reports-status-filter.warning { background:#FFC107; color:#333; }
.reports-status-filter.critical { background:#F44336; color:#fff; }
.reports-status-filter.unknown { background:#64748b; color:#fff; }
.reports-status-filter.active { box-shadow: 0 0 0 2px #fff; opacity:1; }
.reports-status-filter:not(.active) { filter: grayscale(0.5) opacity(0.5); }

/* Scrollbar styles */
.reports-content {
    scrollbar-width: thin; /* Firefox */
    scrollbar-color: #555 #1a1a1a; /* thumb track */
}

.reports-content::-webkit-scrollbar {
    width: 8px;
}

.reports-content::-webkit-scrollbar-track {
    background: #1a1a1a;
}

.reports-content::-webkit-scrollbar-thumb {
    background-color: #555;
    border-radius: 4px;
}

.reports-content::-webkit-scrollbar-thumb:hover {
    background-color: #777;
}

/* Schedule Report Modal */
.sr-modal{display:none;position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.6);z-index:1000;align-items:center;justify-content:center;}
.sr-modal-content{background:#252525;padding:25px 30px;border-radius:6px;min-width:320px;max-width:600px;color:#fff;}
.sr-modal-content h2{margin-top:0;margin-bottom:15px;font-size:20px;}
.sr-close{float:right;font-size:28px;cursor:pointer;color:#aaa;}
.sr-close:hover{color:#fff;}
.sr-field{display:flex;flex-direction:column;gap:6px;margin-bottom:12px;}
.sr-field label{font-size:14px;color:#e0e0e0;}
.sr-field input, .sr-field select{background:#2c2c2c;border:none;color:#fff;padding:6px 10px;border-radius:4px;}
.sr-actions{display:flex;justify-content:flex-end;gap:10px;margin-top:10px;}

/* Schedule modal enhancements */
.sr-status{font-size:14px;margin-bottom:6px;}
.sr-status.scheduled{color:#cde06b;font-weight:600;}
.sr-status.not-scheduled{color:#d41d28;font-weight:600;}

.sr-modal .generate-btn{background-color:#444;border:none;color:#fff;}
.sr-modal .generate-btn:hover{background-color:#666;}
.sr-modal .generate-btn.red{background-color:#d9534f;}
.sr-modal .generate-btn.red:hover{background-color:#c9302c;}
.sr-modal .generate-btn:first-child{background-color:#28a745;}
.sr-modal .generate-btn:first-child:hover{background-color:#218838;}

/* Saved Reports Container (in modal) */
.saved-reports-container {
    background: #252525;
    border: 2px solid #444;
    border-radius: 8px;
    padding: 20px;
    min-height: 300px;
    max-height: 500px;
    overflow-y: auto;
    box-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

/* Multi-select controls */
.saved-reports-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #444;
}

.saved-reports-title {
    font-size: 16px;
    font-weight: 600;
    color: #e0e0e0;
}

.saved-reports-controls {
    display: flex;
    gap: 8px;
    align-items: center;
}

.select-all-checkbox {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    color: #aaa;
}

.select-all-checkbox input[type="checkbox"] {
    margin: 0;
}

.bulk-delete-btn {
    background: #dc3545;
    color: #fff;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: none;
}

.bulk-delete-btn:hover {
    background: #c82333;
}

.bulk-delete-btn.visible {
    display: inline-block;
}

/* Report item with checkbox */
.saved-report-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: #252525;
    border: 2px solid #444;
    border-radius: 6px;
    transition: all 0.2s ease;
    margin-bottom: 8px;
}

.saved-report-item.selected {
    border-color: #007bff;
    background: #2d2d2d;
    box-shadow: 0 2px 8px rgba(0,123,255,0.2);
}

.saved-report-item:hover {
    background: #2d2d2d;
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0,123,255,0.2);
}

.report-checkbox {
    margin-right: 12px;
}

.report-info {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.report-title {
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 4px;
}

.report-date {
    font-size: 12px;
    color: #ccc;
    margin-bottom: 2px;
}

.report-details {
    font-size: 11px;
    color: #999;
}

.report-actions {
    display: flex;
    gap: 8px;
}

.report-action-btn {
    background: #252525;
    border: 2px solid #555;
    border-radius: 6px;
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    color: #ccc;
}

.report-action-btn:hover {
    background: #2d2d2d;
    transform: translateY(-1px);
}

.view-btn:hover {
    color: #2196F3;
    border-color: #2196F3;
}

.download-btn:hover {
    color: #4CAF50;
    border-color: #4CAF50;
}

.delete-btn:hover {
    color: #f44336;
    border-color: #f44336;
}

.saved-reports-loading,
.saved-reports-empty {
    text-align: center;
    color: #aaaaaa;
    font-size: 14px;
    padding: 20px;
}

.saved-reports-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.saved-report-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: #252525;
    border: 2px solid #444;
    border-radius: 6px;
    transition: all 0.2s ease;
    margin-bottom: 8px;
}

.saved-report-item:hover {
    background: #2d2d2d;
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0,123,255,0.2);
}

.report-info {
    flex: 1;
}

.report-title {
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 4px;
}

.report-date {
    font-size: 12px;
    color: #ccc;
    margin-bottom: 2px;
}

.report-details {
    font-size: 11px;
    color: #999;
}

.report-actions {
    display: flex;
    gap: 8px;
}

.report-action-btn {
    background: #252525;
    border: 2px solid #555;
    border-radius: 6px;
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    color: #ccc;
}

.report-action-btn:hover {
    background: #2d2d2d;
    transform: translateY(-1px);
}

.view-btn:hover {
    color: #2196F3;
    border-color: #2196F3;
}

.download-btn:hover {
    color: #4CAF50;
    border-color: #4CAF50;
}

.delete-btn:hover {
    color: #f44336;
    border-color: #f44336;
}

/* Saved reports badge */
.saved-reports-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #dc3545;
    color: #fff;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 10px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
    min-width: 18px;
}

.generate-btn {
    position: relative;
}
