(function() {
    // Helper: convert seconds to formatted duration (h m s)
    function formatDuration(sec) {
        const h = Math.floor(sec / 3600);
        const m = Math.floor((sec % 3600) / 60);
        const s = sec % 60;
        return `${h}h ${m}m ${s}s`;
    }

    // Helper: local ISO string without seconds for <input type="datetime-local">
    function toLocalIso(dateObj) {
        const tzOffset = dateObj.getTimezoneOffset() * 60000;
        return new Date(dateObj.getTime() - tzOffset).toISOString().slice(0, 16);
    }

    // Helper: fetch hostname -> IP address map via Nagios objectjson API
    async function fetchHostIpMap(){
        try {
            const resp = await fetch('/nagios/cgi-bin/objectjson.cgi?query=hostlist&details=true', { credentials: 'include' });
            if(!resp.ok) throw new Error(resp.statusText);
            const js = await resp.json();
            const map = {};
            const hostlist = js?.data?.hostlist || {};
            Object.entries(hostlist).forEach(([hostname, obj])=>{
                if(obj && obj.address){
                    map[hostname] = obj.address;
                }
            });
            return map;
        } catch(err){
            console.warn('Failed to fetch host IP map', err);
            return {}; // graceful fallback
        }
    }

    // DOM ready
    document.addEventListener('DOMContentLoaded', () => {
        const startInput = document.getElementById('report-start');
        const endInput   = document.getElementById('report-end');
        const typeSel    = document.getElementById('report-type');
        const hostgroupSel = document.getElementById('report-hostgroup');
        const genBtn     = document.getElementById('report-generate');
        const saveBtn    = document.getElementById('report-save');
        const scheduleBtn  = document.getElementById('report-schedule');
        const viewSavedBtn = document.getElementById('view-saved-reports');
        const contentDiv = document.getElementById('reports-content');
        const statusFilterContainer = document.getElementById('reports-status-filters');
        const srDisableBtn = document.getElementById('sr-disable');
        const srStatus     = document.getElementById('sr-status');

        // Initialise date pickers using server time if possible
        function initDates() {
            const progUrl = '/nagios/cgi-bin/statusjson.cgi?query=programstatus';
            fetch(progUrl, { credentials: 'include' })
                .then(r => r.json())
                .then(js => js?.data?.programstatus?.current_time)
                .then(serverSec => {
                    const endDate = serverSec ? new Date(serverSec * 1000) : new Date();
                    endInput.value = toLocalIso(endDate);
                    startInput.value = toLocalIso(new Date(endDate.getTime() - 24 * 60 * 60 * 1000));
                })
                .catch(() => {
                    const now = new Date();
                    endInput.value = toLocalIso(now);
                    startInput.value = toLocalIso(new Date(now.getTime() - 24 * 60 * 60 * 1000));
                });
        }

        // ------------------ Dropdown population helpers ------------------

        // Remove all options except the first ("All")
        function resetSelectOptions(sel) {
            while (sel.options.length > 1) sel.remove(1);
        }

        // Generic helper to append unique option values keeping "All" first
        function appendOptions(arr) {
            const existing = new Set(Array.from(hostgroupSel.options).map(o => o.value));
            arr.sort().forEach(v => {
                if (!existing.has(v)) {
                    const opt = document.createElement('option');
                    opt.value = v;
                    opt.textContent = v;
                    hostgroupSel.appendChild(opt);
                }
            });
        }

        // Populate hostgroups dropdown using availability endpoint only
        function populateHostgroups() {
            resetSelectOptions(hostgroupSel);
            const nowSec = Math.floor(Date.now() / 1000);
            const url = `/nagios/cgi-bin/archivejson.cgi?query=availability&availabilityobjecttype=hostgroups&starttime=${nowSec - 3600}&endtime=${nowSec}`;
            fetch(url, { credentials: 'include' })
                .then(r => (r.ok ? r.json() : Promise.reject(r.status)))
                .then(js => {
                    const arr = js?.data?.hostgroups?.map(g => g.name) || (js?.data?.hostgroup ? [js.data.hostgroup.name] : []);
                    if (arr.length) {
                        appendOptions(arr);
                    } else if (typeof fetchAllHostGroups === 'function') {
                        // ultimate fallback DB
                        fetchAllHostGroups().then(appendOptions);
                    }
                })
                .catch(() => {
                    if (typeof fetchAllHostGroups === 'function') fetchAllHostGroups().then(appendOptions);
                });
        }

        // Populate hosts dropdown using availability endpoint
        function populateHosts() {
            resetSelectOptions(hostgroupSel);
            const nowSec = Math.floor(Date.now() / 1000);
            const url = `/nagios/cgi-bin/archivejson.cgi?query=availability&availabilityobjecttype=hosts&starttime=${nowSec - 3600}&endtime=${nowSec}`;
            fetch(url, { credentials: 'include' })
                .then(r => (r.ok ? r.json() : Promise.reject(r.status)))
                .then(js => {
                    const arr = js?.data?.hosts?.map(h => h.name) || (js?.data?.host ? [js.data.host.name] : []);
                    if (arr.length) {
                        appendOptions(arr);
                    } else if (typeof fetchAllHosts === 'function') {
                        // If there's a helper available for full host list
                        fetchAllHosts().then(appendOptions);
                    }
                })
                .catch(() => {
                    if (typeof fetchAllHosts === 'function') fetchAllHosts().then(appendOptions);
                });
        }

        // Update dropdown + label based on selected report type
        function refreshEntityDropdown() {
            const lbl = document.querySelector('label[for="report-hostgroup"]');
            if (typeSel.value === 'services') {
                lbl.textContent = 'Host';
                populateHosts();
            } else {
                lbl.textContent = 'Hostgroup';
                populateHostgroups();
            }
        }

        initDates();
        refreshEntityDropdown();

        // Attach handlers
        genBtn.addEventListener('click', generateReport);
        if(saveBtn) saveBtn.addEventListener('click', saveReport);
        if(viewSavedBtn) viewSavedBtn.addEventListener('click', openSavedReportsModal);
        if(scheduleBtn){
            scheduleBtn.addEventListener('click', ()=>{
                const scheduleModal = document.getElementById('scheduleReportModal');
                if(!scheduleModal) return;

                // Show modal first (UX) then attempt to load config
                scheduleModal.style.display = 'flex';

                // Reset form to defaults & status message
                document.getElementById('sr-email').value = '';
                document.getElementById('sr-frequency').value = 'daily';
                document.getElementById('sr-time').value = '00:05';
                document.getElementById('sr-range').value = 1;
                // Reset status checkboxes (select all by default)
                document.querySelectorAll('#sr-host-statuses input').forEach(cb => cb.checked = true);
                document.querySelectorAll('#sr-svc-statuses input').forEach(cb => cb.checked = true);
                if(srDisableBtn) srDisableBtn.style.display = 'none';
                if(srStatus){
                    srStatus.textContent = 'Checking current schedule…';
                    srStatus.className = 'sr-status';
                }

                // Fetch current cron config
                fetch('functions/reportsFunctions/getReportCron.php')
                    .then(r=>r.json())
                    .then(js=>{
                        if(!js.success) return;
                        if(js.enabled && js.config){
                            const cfg = js.config;
                            if(cfg.email) document.getElementById('sr-email').value = cfg.email;
                            if(cfg.frequency) document.getElementById('sr-frequency').value = cfg.frequency;
                            if(cfg.time) document.getElementById('sr-time').value = cfg.time;
                            if(cfg.range) document.getElementById('sr-range').value = cfg.range;
                            // Apply stored status filters
                            if(cfg.hostStatuses){
                                const arr = cfg.hostStatuses.split(',');
                                document.querySelectorAll('#sr-host-statuses input').forEach(cb=>{cb.checked = arr.includes(cb.value);});
                            }
                            if(cfg.svcStatuses){
                                const arrS = cfg.svcStatuses.split(',');
                                document.querySelectorAll('#sr-svc-statuses input').forEach(cb=>{cb.checked = arrS.includes(cb.value);});
                            }
                            if(srDisableBtn) srDisableBtn.style.display = 'inline-block';
                            if(srStatus){
                                srStatus.textContent = 'Report is currently scheduled';
                                srStatus.classList.add('scheduled');
                            }
                        } else {
                            if(srStatus){
                                srStatus.textContent = 'No report scheduled';
                                srStatus.classList.add('not-scheduled');
                            }
                        }
                    })
                    .catch(err=>{
                        console.warn('Failed to fetch cron config', err);
                        if(srStatus){
                            srStatus.textContent = 'Unable to retrieve schedule status';
                        }
                    });
            });
        }

        // update filters when type changes
        typeSel.addEventListener('change', () => {
            buildStatusFilters(typeSel.value);
            refreshEntityDropdown();
        });

        // ------------- Status options & state (moved up to avoid TDZ) -------------
        const hostStatusOptions = [
            { key: 'up', label: 'Up', class: 'ok' },
            { key: 'down', label: 'Down', class: 'critical' },
            { key: 'unreachable', label: 'Unreach', class: 'unknown' }
        ];

        const serviceStatusOptions = [
            { key: 'ok', label: 'OK', class: 'ok' },
            { key: 'warning', label: 'Warn', class: 'warning' },
            { key: 'critical', label: 'Crit', class: 'critical' },
            { key: 'unknown', label: 'Unk', class: 'unknown' }
        ];

        let activeStatuses = new Set();

        // Build filters initially now that constants exist
        buildStatusFilters(typeSel.value);

        // Main generator
        async function generateReport() {
            const type = typeSel.value; // hostgroups | services
            const startTs = Math.floor(new Date(startInput.value).getTime() / 1000);
            const endTs   = Math.floor(new Date(endInput.value).getTime() / 1000);
            if (isNaN(startTs) || isNaN(endTs) || endTs <= startTs) {
                alert('Invalid time range');
                return;
            }

            let url = `/nagios/cgi-bin/archivejson.cgi?query=availability&availabilityobjecttype=${encodeURIComponent(type)}&starttime=${startTs}&endtime=${endTs}`;
            const entityVal = hostgroupSel.value;
            if (entityVal && entityVal !== 'all') {
                if (type === 'services') {
                    url += `&hostname=${encodeURIComponent(entityVal)}`;
                } else {
                    url += `&hostgroup=${encodeURIComponent(entityVal)}`;
                }
            }

            // Loading indicator
            contentDiv.innerHTML = '<div class="reports-loading"><i class="fa fa-spinner fa-spin"></i> Loading...</div>';

            try {
                // Fetch availability data and host IP map in parallel for efficiency
                const [resp, hostIpMap] = await Promise.all([
                    fetch(url, { credentials: 'include' }),
                    fetchHostIpMap()
                ]);
                if (!resp.ok) throw new Error(resp.statusText);
                const data = await resp.json();
                
                // Store the original data for PDF summary calculation
                window.lastReportData = data;
                
                renderTable(data, type, hostIpMap);
                applyStatusFilter();
            } catch (err) {
                console.error('Report fetch error', err);
                contentDiv.innerHTML = '<div class="reports-error">Error fetching report data</div>';
            }
        }

        // Render functions
        function renderTable(json, type, ipMap = {}) {
            let html = '';
            
            // Helper function to calculate summary statistics
            function calculateSummary(data, type) {
                if (type === 'services') {
                    const services = data?.data?.services || [];
                    if (!services.length) return null;
                    
                    let totalServices = services.length;
                    let servicesWithWarnings = 0;
                    let servicesWithCritical = 0;
                    let servicesWithUnknown = 0;
                    
                    services.forEach(svc => {
                        if ((+svc.time_warning || 0) > 0) servicesWithWarnings++;
                        if ((+svc.time_critical || 0) > 0) servicesWithCritical++;
                        if ((+svc.time_unknown || 0) > 0) servicesWithUnknown++;
                    });
                    
                    return {
                        total: totalServices,
                        warning: servicesWithWarnings,
                        critical: servicesWithCritical,
                        unknown: servicesWithUnknown,
                        warningPct: (servicesWithWarnings / totalServices * 100).toFixed(1),
                        criticalPct: (servicesWithCritical / totalServices * 100).toFixed(1),
                        unknownPct: (servicesWithUnknown / totalServices * 100).toFixed(1)
                    };
                } else {
                    const hostgroups = data?.data?.hostgroups || [];
                    const grpArr = Array.isArray(hostgroups) && hostgroups.length > 0 ? hostgroups : (data?.data?.hostgroup ? [data.data.hostgroup] : []);
                    
                    if (!grpArr.length) return null;
                    
                    let totalHosts = 0;
                    let hostsWithDown = 0;
                    let hostsWithUnreachable = 0;
                    
                    grpArr.forEach(g => {
                        const hostList = g.hosts || [];
                        totalHosts += hostList.length;
                        hostList.forEach(h => {
                            if ((+h.time_down || 0) > 0) hostsWithDown++;
                            if ((+h.time_unreachable || 0) > 0) hostsWithUnreachable++;
                        });
                    });
                    
                    return {
                        total: totalHosts,
                        down: hostsWithDown,
                        unreachable: hostsWithUnreachable,
                        downPct: (hostsWithDown / totalHosts * 100).toFixed(1),
                        unreachablePct: (hostsWithUnreachable / totalHosts * 100).toFixed(1)
                    };
                }
            }
            
            // Helper function to get status colors based on theme
            function getStatusColor(status) {
                // Check if we're in dark theme by looking at body class or background color
                const isDarkTheme = document.body.classList.contains('dark-theme') || 
                                  getComputedStyle(document.body).backgroundColor.includes('rgb(26, 26, 26)');
                
                if (isDarkTheme) {
                    // Dark theme colors from reports.css
                    switch(status) {
                        case 'success': return '#cde06b';
                        case 'warning': return '#ffa500';
                        case 'critical': return '#d41d28';
                        case 'unknown': return '#64748b';
                        default: return '#ccc';
                    }
                } else {
                    // Light theme colors from reports.css
                    switch(status) {
                        case 'success': return '#4CAF50';
                        case 'warning': return '#FFC107';
                        case 'critical': return '#F44336';
                        case 'unknown': return '#64748b';
                        default: return '#ccc';
                    }
                }
            }
            
            // Helper function to create pie chart
            function createPieChart(containerId, data, type) {
                const container = document.getElementById(containerId);
                if (!container || !data || data.length === 0) return;
                
                // Clear previous content
                container.innerHTML = '';
                
                // Create chart wrapper
                const chartWrapper = document.createElement('div');
                chartWrapper.className = 'pie-chart-wrapper';
                container.appendChild(chartWrapper);
                
                // Create SVG container
                const size = 200;
                const radius = size / 2;
                
                const svg = d3.select(chartWrapper)
                    .append('svg')
                    .attr('viewBox', `0 0 ${size} ${size}`)
                    .attr('preserveAspectRatio', 'xMidYMid meet')
                    .style('width', '200px')
                    .style('height', '200px');
                
                const g = svg.append('g').attr('transform', `translate(${radius},${radius})`);
                
                // Create pie chart
                const pie = d3.pie().value(d => d.value);
                const arc = d3.arc().innerRadius(radius * 0.3).outerRadius(radius * 0.8);
                
                // Add slices
                g.selectAll('path')
                    .data(pie(data))
                    .enter()
                    .append('path')
                    .attr('d', arc)
                    .attr('fill', d => d.data.color)
                    .attr('stroke', '#fff')
                    .attr('stroke-width', 2)
                    .append('title')
                    .text(d => `${d.data.label}: ${d.data.value} (${((d.data.value / data.reduce((sum, item) => sum + item.value, 0)) * 100).toFixed(1)}%)`);
                
                // Add legend
                const legendContainer = document.createElement('div');
                legendContainer.className = 'pie-chart-legend';
                container.appendChild(legendContainer);
                
                data.forEach(d => {
                    const legendItem = document.createElement('div');
                    legendItem.className = 'pie-legend-item';
                    
                    const colorBox = document.createElement('span');
                    colorBox.className = 'pie-legend-color';
                    colorBox.style.background = d.color;
                    
                    const label = document.createElement('span');
                    label.className = 'pie-legend-label';
                    const total = data.reduce((sum, item) => sum + item.value, 0);
                    const percentage = ((d.value / total) * 100).toFixed(1);
                    label.textContent = `${d.label}: ${d.value} (${percentage}%)`;
                    
                    legendItem.appendChild(colorBox);
                    legendItem.appendChild(label);
                    legendContainer.appendChild(legendItem);
                });
            }

            // Helper function to prepare Marimekko chart data
            function prepareMarimekkoData(json, type) {
                const data = [];

                if (type === 'services') {
                    const services = json?.data?.services || [];
                    if (!services.length) return [];

                    // Group services by host and calculate actual percentages
                    const hostMap = services.reduce((map, svc) => {
                        if (!map[svc.host_name]) {
                            map[svc.host_name] = {
                                name: svc.host_name,
                                criticalTime: 0,
                                warningTime: 0,
                                unknownTime: 0,
                                totalTime: 0
                            };
                        }

                        // Sum up actual time values across all services for this host
                        map[svc.host_name].criticalTime += (+svc.time_critical || 0);
                        map[svc.host_name].warningTime += (+svc.time_warning || 0);
                        map[svc.host_name].unknownTime += (+svc.time_unknown || 0);
                        map[svc.host_name].totalTime += (+svc.time_ok || 0) + (+svc.time_warning || 0) + (+svc.time_critical || 0) + (+svc.time_unknown || 0);

                        return map;
                    }, {});

                    // Convert to array and calculate percentages
                    Object.values(hostMap).forEach(host => {
                        if (host.totalTime > 0) {
                            const criticalPct = (host.criticalTime / host.totalTime) * 100;
                            const warningPct = (host.warningTime / host.totalTime) * 100;
                            const unknownPct = (host.unknownTime / host.totalTime) * 100;
                            const totalIssuesPct = criticalPct + warningPct + unknownPct;

                            if (totalIssuesPct > 0) {
                                host.totalIssues = totalIssuesPct;
                                host.sections = [];

                                if (criticalPct > 0) {
                                    host.sections.push({
                                        label: 'Critical',
                                        value: criticalPct,
                                        color: getStatusColor('critical'),
                                        parent: host
                                    });
                                }
                                if (warningPct > 0) {
                                    host.sections.push({
                                        label: 'Warning',
                                        value: warningPct,
                                        color: getStatusColor('warning'),
                                        parent: host
                                    });
                                }
                                if (unknownPct > 0) {
                                    host.sections.push({
                                        label: 'Unknown',
                                        value: unknownPct,
                                        color: getStatusColor('unknown'),
                                        parent: host
                                    });
                                }

                                data.push(host);
                            }
                        }
                    });
                } else {
                    // For hosts report
                    const hostgroups = json?.data?.hostgroups || [];
                    const grpArr = Array.isArray(hostgroups) && hostgroups.length > 0 ? hostgroups : (json?.data?.hostgroup ? [json.data.hostgroup] : []);

                    grpArr.forEach(g => {
                        const hostList = g.hosts || [];
                        hostList.forEach(h => {
                            const total = (+h.time_up || 0) + (+h.time_down || 0) + (+h.time_unreachable || 0);
                            if (total > 0) {
                                const downPct = ((+h.time_down || 0) / total) * 100;
                                const unreachablePct = ((+h.time_unreachable || 0) / total) * 100;
                                const totalIssuesPct = downPct + unreachablePct;

                                if (totalIssuesPct > 0) {
                                    const host = {
                                        name: h.name,
                                        totalIssues: totalIssuesPct,
                                        sections: []
                                    };

                                    if (downPct > 0) {
                                        host.sections.push({
                                            label: 'Down',
                                            value: downPct,
                                            color: getStatusColor('critical'),
                                            parent: host
                                        });
                                    }
                                    if (unreachablePct > 0) {
                                        host.sections.push({
                                            label: 'Unreachable',
                                            value: unreachablePct,
                                            color: getStatusColor('unknown'),
                                            parent: host
                                        });
                                    }

                                    data.push(host);
                                }
                            }
                        });
                    });
                }

                // Sort by total issues percentage (descending) for better visualization
                data.sort((a, b) => b.totalIssues - a.totalIssues);

                return data;
            }

            // Helper function to create Marimekko chart
            function createMarimekkoChart(containerId, data, type) {
                const container = document.getElementById(containerId);
                if (!container || !data || data.length === 0) return;

                // Clear previous content
                container.innerHTML = '';

                // Create chart wrapper
                const chartWrapper = document.createElement('div');
                chartWrapper.className = 'marimekko-chart-wrapper';
                container.appendChild(chartWrapper);

                // Chart dimensions
                const margin = { top: 20, right: 20, bottom: 20, left: 20 };
                const width = 500 - margin.left - margin.right;
                const height = 300 - margin.top - margin.bottom;

                // Create SVG
                const svg = d3.select(chartWrapper)
                    .append('svg')
                    .attr('viewBox', `0 0 ${width + margin.left + margin.right} ${height + margin.top + margin.bottom}`)
                    .attr('preserveAspectRatio', 'xMidYMid meet')
                    .style('width', '100%')
                    .style('height', 'auto')
                    .style('max-width', '500px');

                const g = svg.append('g')
                    .attr('transform', `translate(${margin.left},${margin.top})`);

                if (data.length === 0) {
                    container.innerHTML = '<div class="marimekko-empty">No issues to display</div>';
                    return;
                }

                // Calculate total sum for width scaling
                const totalSum = data.reduce((sum, d) => sum + d.totalIssues, 0);

                // Calculate positions and dimensions for each segment
                let currentX = 0;
                data.forEach(segment => {
                    // Width is proportional to total issues percentage
                    segment.width = (segment.totalIssues / totalSum) * width;
                    segment.x = currentX;
                    currentX += segment.width;

                    // Calculate subsection positions within this segment
                    let currentY = 0;
                    segment.sections.forEach(section => {
                        // Height is proportional to this section's percentage within the segment
                        section.height = (section.value / segment.totalIssues) * height;
                        section.y = currentY;
                        currentY += section.height;
                    });
                });

                // Create segments
                const segmentGroups = g.selectAll('.segment')
                    .data(data)
                    .enter()
                    .append('g')
                    .attr('class', 'segment')
                    .attr('transform', d => `translate(${d.x}, 0)`);

                // Add rectangles for each section within segments
                segmentGroups.selectAll('.section')
                    .data(d => d.sections)
                    .enter()
                    .append('rect')
                    .attr('class', 'section')
                    .attr('x', 0)
                    .attr('y', d => d.y)
                    .attr('width', d => d.parent.width)
                    .attr('height', d => d.height)
                    .attr('fill', d => d.color)
                    .attr('stroke', '#fff')
                    .attr('stroke-width', 1)
                    .append('title')
                    .text(d => `${d.parent.name}: ${d.label} - ${d.value.toFixed(1)}%`);

                // Add text labels for segments (host names)
                segmentGroups.each(function(d) {
                    const group = d3.select(this);

                    // Only add text if the segment is wide enough
                    if (d.width > 40) {
                        group.append('text')
                            .attr('x', d.width / 2)
                            .attr('y', height / 2)
                            .attr('text-anchor', 'middle')
                            .attr('dominant-baseline', 'middle')
                            .attr('font-size', Math.min(10, d.width / 8) + 'px')
                            .attr('fill', '#fff')
                            .attr('font-weight', 'bold')
                            .text(d.name.length > 8 ? d.name.substring(0, 8) + '...' : d.name)
                            .append('title')
                            .text(d.name);

                        // Add percentage label
                        group.append('text')
                            .attr('x', d.width / 2)
                            .attr('y', height / 2 + 12)
                            .attr('text-anchor', 'middle')
                            .attr('dominant-baseline', 'middle')
                            .attr('font-size', Math.min(9, d.width / 10) + 'px')
                            .attr('fill', '#fff')
                            .text(`${d.totalIssues.toFixed(1)}%`);
                    }
                });
            }

            // Generate summary section
            const summary = calculateSummary(json, type);
            if (summary) {
                html += '<div class="report-summary">';
                if (type === 'services') {
                    html += '<h2 class="summary-title">Summary of All Monitored Services</h2>';
                } else {
                    html += '<h2 class="summary-title">Summary of All Monitored Hosts</h2>';
                }
                html += '<div class="charts-container">';
                html += '<div class="chart-section">';
                html += '<h3 class="chart-title">Overall Distribution</h3>';
                html += '<div id="summary-pie-chart" class="summary-pie-chart"></div>';
                html += '</div>';
                html += '<div class="chart-section">';
                html += '<h3 class="chart-title">Host/Service Issues Breakdown</h3>';
                html += '<div id="summary-marimekko-chart" class="summary-marimekko-chart"></div>';
                html += '</div>';
                html += '</div>';
                html += '</div>';
            }
            
            if (type === 'services') {
                const services = json?.data?.services || [];
                if (!services.length) {
                    contentDiv.innerHTML = '<div class="reports-empty">No data for selected parameters</div>';
                    return;
                }

                // --- Build host -> services map -----
                const hostMap = services.reduce((map, svc) => {
                    if (!map[svc.host_name]) map[svc.host_name] = [];
                    map[svc.host_name].push(svc);
                    return map;
                }, {});

                // --- Render each host section -----
                Object.keys(hostMap).sort().forEach(host => {
                    const ip = ipMap[host];
                    const hostDisplay = ip && ip !== host ? `${host} (${ip})` : host;
                    html += `<h3 class="report-title">${hostDisplay}</h3>`;
                    html += '<table class="report-table service-table"><thead><tr><th>Service</th><th>OK %</th><th>Warn %</th><th>Crit %</th><th>Unk %</th></tr></thead><tbody>';

                    hostMap[host].forEach(svc => {
                        const total = (+svc.time_ok || 0) + (+svc.time_warning || 0) + (+svc.time_critical || 0) + (+svc.time_unknown || 0);
                        const pctNumOk = total ? ((+svc.time_ok || 0)/total)*100 : 0;
                        const pctNumWarn = total ? ((+svc.time_warning || 0)/total)*100 : 0;
                        const pctNumCrit = total ? ((+svc.time_critical || 0)/total)*100 : 0;
                        const pctNumUnk = total ? ((+svc.time_unknown || 0)/total)*100 : 0;
                        const fmt = n => n.toFixed(1);
                        // Determine all applicable statuses for filtering
                        const statuses = [];
                        if ((+svc.time_critical||0)>0) statuses.push('critical');
                        if ((+svc.time_warning||0)>0) statuses.push('warning');
                        if ((+svc.time_unknown||0)>0) statuses.push('unknown');
                        if ((+svc.time_ok||0)>0) statuses.push('ok');
                        
                        // Use primary status for display (prioritize critical > warning > unknown > ok)
                        const primaryStatus = (+svc.time_critical||0)>0 ? 'critical' : ((+svc.time_warning||0)>0 ? 'warning' : ((+svc.time_unknown||0)>0 ? 'unknown' : 'ok'));
                        
                        // Debug: Log service details
                        console.log('Service:', svc.description, 'Critical:', +svc.time_critical, 'Unknown:', +svc.time_unknown, 'Statuses:', statuses, 'Primary:', primaryStatus);
                        
                        html += `<tr data-status="${primaryStatus}" data-all-statuses="${statuses.join(',')}">
                            <td>${svc.description}</td>
                            <td${pctNumOk>0? ' class="ok"':''}>${fmt(pctNumOk)}</td>
                            <td${pctNumWarn>0? ' class="warning"':''}>${fmt(pctNumWarn)}</td>
                            <td${pctNumCrit>0? ' class="critical"':''}>${fmt(pctNumCrit)}</td>
                            <td${pctNumUnk>0? ' class="unknown"':''}>${fmt(pctNumUnk)}</td>
                        </tr>`;
                    });
                    html += '</tbody></table>';
                });
            } else if (type === 'hostgroups') {
                const hostgroups = json?.data?.hostgroups || [];
                // Handle both array and single-object responses
                const grpArr = Array.isArray(hostgroups) && hostgroups.length > 0 ? hostgroups : (json?.data?.hostgroup ? [json.data.hostgroup] : []);

                if (!grpArr.length) {
                    contentDiv.innerHTML = '<div class="reports-empty">No data for selected parameters</div>';
                    return;
                }

                grpArr.forEach(g => {
                    const hostList = g.hosts || [];
                    if (!hostList.length) return; // Skip empty hostgroups

                    html += `<h3 class="report-title">${g.name}</h3>`;
                    html += '<table class="report-table hostgroup-table"><thead><tr><th>Host</th><th>Up %</th><th>Down %</th><th>Unreachable %</th></tr></thead><tbody>';
                    hostList.forEach(h => {
                        const total = (+h.time_up || 0) + (+h.time_down || 0) + (+h.time_unreachable || 0);
                        const pctNumUp = total ? ((+h.time_up || 0)/total)*100 : 0;
                        const pctNumDown = total ? ((+h.time_down || 0)/total)*100 : 0;
                        const pctNumUnreach = total ? ((+h.time_unreachable || 0)/total)*100 : 0;
                        const fmtH = n=>n.toFixed(1);
                        const status = (+h.time_down||0)>0? 'down' : ((+h.time_unreachable||0)>0 ? 'unreachable':'up');
                        const ip = ipMap[h.name];
                        const hostDisplay = ip && ip !== h.name ? `${h.name} (${ip})` : h.name;
                        html += `<tr data-status="${status}">
                                <td>${hostDisplay}</td>
                                <td${pctNumUp>0? ' class="ok"':''}>${fmtH(pctNumUp)}</td>
                                <td${pctNumDown>0? ' class="critical"':''}>${fmtH(pctNumDown)}</td>
                                <td${pctNumUnreach>0? ' class="unknown"':''}>${fmtH(pctNumUnreach)}</td>
                            </tr>`;
                    });
                    html += '</tbody></table>';
                });
            } else {
                contentDiv.innerHTML = '<div class="reports-empty">Unsupported report type</div>';
                return;
            }
            
            // Set the HTML content first
            contentDiv.innerHTML = html;
            
            // Create pie chart if summary exists
            if (summary) {
                let pieData = [];
                
                if (type === 'services') {
                    // Services pie chart data
                    const okCount = summary.total - summary.warning - summary.critical - summary.unknown;
                    pieData = [
                        { label: 'Services Operating Normally', value: okCount, color: getStatusColor('success') },
                        { label: 'Services with Warning State', value: summary.warning, color: getStatusColor('warning') },
                        { label: 'Services with Critical State', value: summary.critical, color: getStatusColor('critical') },
                        { label: 'Services with Unknown State', value: summary.unknown, color: getStatusColor('unknown') }
                    ].filter(d => d.value > 0);
                } else {
                    // Hosts pie chart data
                    const upCount = summary.total - summary.down - summary.unreachable;
                    pieData = [
                        { label: 'Hosts Operating Normally', value: upCount, color: getStatusColor('success') },
                        { label: 'Hosts with Down State', value: summary.down, color: getStatusColor('critical') },
                        { label: 'Hosts with Unreachable State', value: summary.unreachable, color: getStatusColor('unknown') }
                    ].filter(d => d.value > 0);
                }
                
                // Create the pie chart
                if (pieData.length > 0) {
                    createPieChart('summary-pie-chart', pieData, type);
                }

                // Prepare Marimekko chart data
                const marimekkoData = prepareMarimekkoData(json, type);
                console.log('Marimekko data:', marimekkoData);
                if (marimekkoData.length > 0) {
                    createMarimekkoChart('summary-marimekko-chart', marimekkoData, type);
                } else {
                    console.log('No Marimekko data available');
                }
            }
        }

        // ---------------- Status Filtering ------------------

        function buildStatusFilters(type){
            statusFilterContainer.innerHTML = '';
            const opts = type === 'services' ? serviceStatusOptions : hostStatusOptions;
            activeStatuses = new Set(opts.map(o=>o.key));
            opts.forEach(o=>{
                const btn = document.createElement('button');
                btn.className = `reports-status-filter ${o.class} active`;
                btn.dataset.statusKey = o.key;
                btn.title = o.label;
                btn.textContent = o.label;
                btn.addEventListener('click', ()=>{
                    if (btn.classList.contains('active')){
                        btn.classList.remove('active');
                        activeStatuses.delete(o.key);
                    } else {
                        btn.classList.add('active');
                        activeStatuses.add(o.key);
                    }
                    applyStatusFilter();
                });
                statusFilterContainer.appendChild(btn);
            });
        }

        function applyStatusFilter(){
            const rows = contentDiv.querySelectorAll('tr[data-status]');
            rows.forEach(row=>{
                const st = row.dataset.status;
                const allStatuses = row.dataset.allStatuses ? row.dataset.allStatuses.split(',') : [st];
                
                // Show row if ANY of its statuses are active
                const shouldShow = allStatuses.some(status => activeStatuses.has(status));
                row.style.display = shouldShow ? '' : 'none';
            });

            // Hide or show entire tables (and their titles) based on visible rows
            const tables = contentDiv.querySelectorAll('table.report-table');
            tables.forEach(table => {
                const hasVisibleRow = Array.from(table.querySelectorAll('tbody tr')).some(r => r.style.display !== 'none');
                const titleEl = table.previousElementSibling;
                if (hasVisibleRow) {
                    table.style.display = '';
                    if (titleEl && titleEl.classList.contains('report-title')) titleEl.style.display = '';
                } else {
                    table.style.display = 'none';
                    if (titleEl && titleEl.classList.contains('report-title')) titleEl.style.display = 'none';
                }
            });
        }

        // ---------------- Save Report to Server -----------------------
        async function saveReport() {
            // Ensure there is content to save
            if (!contentDiv.querySelector('table')) {
                alert('Nothing to save');
                return;
            }

            const type = typeSel.value;
            const startTs = Math.floor(new Date(startInput.value).getTime() / 1000);
            const endTs = Math.floor(new Date(endInput.value).getTime() / 1000);
            
            if (isNaN(startTs) || isNaN(endTs) || endTs <= startTs) {
                alert('Invalid time range');
                return;
            }

            // Show the name input modal
            const saveReportNameModal = document.getElementById('saveReportNameModal');
            const reportNameInput = document.getElementById('report-name');
            const saveReportForm = document.getElementById('save-report-form');
            
            if (saveReportNameModal) {
                reportNameInput.value = '';
                saveReportNameModal.style.display = 'flex';
                reportNameInput.focus();
            }
        }

        // Handle save report form submission
        async function handleSaveReportForm(customName = '') {
            const type = typeSel.value;
            const startTs = Math.floor(new Date(startInput.value).getTime() / 1000);
            const endTs = Math.floor(new Date(endInput.value).getTime() / 1000);
            
            if (isNaN(startTs) || isNaN(endTs) || endTs <= startTs) {
                alert('Invalid time range');
                return;
            }

            // Show loading state
            const saveReportConfirm = document.getElementById('save-report-confirm');
            const originalContent = saveReportConfirm.innerHTML;
            saveReportConfirm.disabled = true;
            saveReportConfirm.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Saving...';

            try {
                const params = new URLSearchParams();
                params.append('type', type);
                params.append('startTs', startTs);
                params.append('endTs', endTs);
                params.append('hostStatuses', Array.from(document.querySelectorAll('#reports-status-filters .reports-status-filter.active')).map(btn => btn.dataset.statusKey).join(','));
                params.append('svcStatuses', Array.from(document.querySelectorAll('#reports-status-filters .reports-status-filter.active')).map(btn => btn.dataset.statusKey).join(','));
                params.append('hostgroup', hostgroupSel.value);
                if (customName) {
                    params.append('customName', customName);
                }

                const response = await fetch('functions/reportsFunctions/saveReport.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: params.toString()
                });

                const result = await response.json();
                
                if (result.success) {
                    // Close the modal
                    const saveReportNameModal = document.getElementById('saveReportNameModal');
                    if (saveReportNameModal) {
                        saveReportNameModal.style.display = 'none';
                    }
                    
                    // Refresh saved reports list if it exists
                    if (typeof loadSavedReports === 'function') {
                        loadSavedReports();
                    }
                } else {
                    alert('Failed to save report: ' + (result.message || 'Unknown error'));
                }
            } catch (error) {
                console.error('Save report error:', error);
                alert('Error saving report');
            } finally {
                // Restore button state
                saveReportConfirm.disabled = false;
                saveReportConfirm.innerHTML = originalContent;
            }
        }

        // ---------------- Saved Reports Management -----------------------
        let savedReports = [];

        async function loadSavedReports() {
            try {
                const response = await fetch('functions/reportsFunctions/listSavedReports.php');
                const result = await response.json();
                
                if (result.success) {
                    savedReports = result.reports || [];
                    displaySavedReports();
                    updateSavedReportsBadge();
                }
            } catch (error) {
                console.error('Failed to load saved reports:', error);
            }
        }

        function updateSavedReportsBadge() {
            const badge = document.getElementById('saved-reports-badge');
            if (badge) {
                const count = savedReports.length;
                if (count > 0) {
                    badge.textContent = count > 99 ? '99+' : count.toString();
                    badge.style.display = 'flex';
                } else {
                    badge.style.display = 'none';
                }
            }
        }

        function displaySavedReports() {
            const savedReportsContainer = document.getElementById('saved-reports-container');
            if (!savedReportsContainer) return;

            if (savedReports.length === 0) {
                savedReportsContainer.innerHTML = '<div class="saved-reports-empty">No saved reports found</div>';
                return;
            }

            let html = `
                <div class="saved-reports-header">
                    <div class="saved-reports-title">Reports (${savedReports.length})</div>
                    <div class="saved-reports-controls">
                        <label class="select-all-checkbox">
                            <input type="checkbox" id="select-all-reports">
                            Select All
                        </label>
                        <button class="bulk-delete-btn" id="bulk-delete-btn" style="display: none;">
                            <i class="fa fa-trash"></i> Delete Selected
                        </button>
                    </div>
                </div>
                <div class="saved-reports-list">`;
            
            savedReports.forEach(report => {
                const date = new Date(report.created * 1000);
                const dateStr = date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
                const reportName = report.customName || `${report.type === 'hostgroups' ? 'Hosts' : 'Services'} Report`;
                
                html += `<div class="saved-report-item" data-filename="${report.filename}">
                    <div class="report-checkbox">
                        <input type="checkbox" class="report-select-checkbox" data-filename="${report.filename}">
                    </div>
                    <div class="report-info">
                        <div class="report-title">${reportName}</div>
                        <div class="report-date">${dateStr}</div>
                        <div class="report-details">
                            ${report.rangeDays} day(s) | ${report.fileSizeFormatted}
                            ${report.scheduled ? ' | Scheduled' : ''}
                        </div>
                    </div>
                    <div class="report-actions">
                        <button class="report-action-btn view-btn" onclick="viewReport('${report.filename}')" title="View">
                            <i class="fa fa-eye"></i>
                        </button>
                        <button class="report-action-btn download-btn" onclick="downloadReport('${report.filename}')" title="Download">
                            <i class="fa fa-download"></i>
                        </button>
                        <button class="report-action-btn delete-btn" onclick="deleteReport('${report.filename}')" title="Delete">
                            <i class="fa fa-trash"></i>
                        </button>
                    </div>
                </div>`;
            });
            html += '</div>';
            savedReportsContainer.innerHTML = html;

            // Add event listeners for multi-select functionality
            setupMultiSelectHandlers();
        }

        // Global functions for report actions
        window.viewReport = function(filename) {
            window.open('functions/reportsFunctions/downloadReport.php?file=' + encodeURIComponent(filename) + '&action=view', '_blank');
        };

        window.downloadReport = function(filename) {
            window.open('functions/reportsFunctions/downloadReport.php?file=' + encodeURIComponent(filename) + '&action=download', '_blank');
        };

        window.deleteReport = async function(filename) {
            if (!confirm('Are you sure you want to delete this report?')) return;

            try {
                const response = await fetch('functions/reportsFunctions/deleteReport.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: 'file=' + encodeURIComponent(filename)
                });

                const result = await response.json();
                
                if (result.success) {
                    // Refresh the list
                    loadSavedReports();
                    // Update badge count
                    updateSavedReportsBadge();
                } else {
                    alert('Failed to delete report: ' + (result.message || 'Unknown error'));
                }
            } catch (error) {
                console.error('Delete report error:', error);
                alert('Error deleting report');
            }
        };

        // Multi-select functionality
        function setupMultiSelectHandlers() {
            const selectAllCheckbox = document.getElementById('select-all-reports');
            const bulkDeleteBtn = document.getElementById('bulk-delete-btn');
            const reportCheckboxes = document.querySelectorAll('.report-select-checkbox');

            if (selectAllCheckbox) {
                selectAllCheckbox.addEventListener('change', function() {
                    const isChecked = this.checked;
                    reportCheckboxes.forEach(checkbox => {
                        checkbox.checked = isChecked;
                        updateReportItemSelection(checkbox);
                    });
                    updateBulkDeleteButton();
                });
            }

            reportCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    updateReportItemSelection(this);
                    updateSelectAllCheckbox();
                    updateBulkDeleteButton();
                });
            });

            if (bulkDeleteBtn) {
                bulkDeleteBtn.addEventListener('click', bulkDeleteReports);
            }
        }

        function updateReportItemSelection(checkbox) {
            const reportItem = checkbox.closest('.saved-report-item');
            if (checkbox.checked) {
                reportItem.classList.add('selected');
            } else {
                reportItem.classList.remove('selected');
            }
        }

        function updateSelectAllCheckbox() {
            const selectAllCheckbox = document.getElementById('select-all-reports');
            const reportCheckboxes = document.querySelectorAll('.report-select-checkbox');
            const checkedCount = document.querySelectorAll('.report-select-checkbox:checked').length;
            
            if (selectAllCheckbox) {
                selectAllCheckbox.checked = checkedCount === reportCheckboxes.length && reportCheckboxes.length > 0;
                selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < reportCheckboxes.length;
            }
        }

        function updateBulkDeleteButton() {
            const bulkDeleteBtn = document.getElementById('bulk-delete-btn');
            const checkedCount = document.querySelectorAll('.report-select-checkbox:checked').length;
            
            if (bulkDeleteBtn) {
                if (checkedCount > 0) {
                    bulkDeleteBtn.style.display = 'inline-block';
                    bulkDeleteBtn.textContent = `Delete Selected (${checkedCount})`;
                } else {
                    bulkDeleteBtn.style.display = 'none';
                }
            }
        }

        async function bulkDeleteReports() {
            const selectedCheckboxes = document.querySelectorAll('.report-select-checkbox:checked');
            if (selectedCheckboxes.length === 0) return;

            const filenames = Array.from(selectedCheckboxes).map(cb => cb.dataset.filename);
            const confirmMessage = `Are you sure you want to delete ${filenames.length} report(s)?`;
            
            if (!confirm(confirmMessage)) return;

            try {
                const response = await fetch('functions/reportsFunctions/deleteMultipleReports.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: 'files=' + encodeURIComponent(JSON.stringify(filenames))
                });

                const result = await response.json();
                
                if (result.success) {
                    // Refresh the list
                    loadSavedReports();
                    // Update badge count
                    updateSavedReportsBadge();
                } else {
                    alert('Failed to delete reports: ' + (result.message || 'Unknown error'));
                }
            } catch (error) {
                console.error('Bulk delete error:', error);
                alert('Error deleting reports');
            }
        }

        // Load saved reports on page load
        loadSavedReports();

        // Function to open saved reports modal
        function openSavedReportsModal() {
            const savedReportsModal = document.getElementById('savedReportsModal');
            if (!savedReportsModal) return;
            
            savedReportsModal.style.display = 'flex';
            // Refresh the saved reports list when opening
            loadSavedReports();
        }

        const scheduleModal = document.getElementById('scheduleReportModal');
        const savedReportsModal = document.getElementById('savedReportsModal');
        const saveReportNameModal = document.getElementById('saveReportNameModal');
        const srClose = document.querySelectorAll('.sr-close');
        const srCancel = document.getElementById('sr-cancel');
        const srForm   = document.getElementById('schedule-form');
        const srSendNow = document.getElementById('sr-send-now');
        const saveReportForm = document.getElementById('save-report-form');
        const saveReportCancel = document.getElementById('save-report-cancel');

        // Close modal handlers
        srClose.forEach(closeBtn => {
            closeBtn.addEventListener('click', () => {
                const modal = closeBtn.closest('.sr-modal');
                if (modal) modal.style.display = 'none';
            });
        });
        
        // Click outside modal to close
        document.addEventListener('click', (event) => {
            const modals = document.querySelectorAll('.sr-modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        });
        
        if(srCancel){srCancel.addEventListener('click', ()=>{scheduleModal.style.display='none';});}
        
        // Save report name modal handlers
        if(saveReportCancel){
            saveReportCancel.addEventListener('click', ()=>{
                if(saveReportNameModal) saveReportNameModal.style.display='none';
            });
        }
        
        if(saveReportForm){
            saveReportForm.addEventListener('submit', function(e){
                e.preventDefault();
                const reportName = document.getElementById('report-name').value.trim();
                handleSaveReportForm(reportName);
            });
        }
        
        // Send Now button handler
        if(srSendNow){
            srSendNow.addEventListener('click', function(){
                const email = document.getElementById('sr-email').value.trim();
                const range = parseInt(document.getElementById('sr-range').value,10) || 1;

                // Validate multiple emails separated by semicolons
                const emails = email.split(';').map(e => e.trim()).filter(e => e.length > 0);
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                for (const singleEmail of emails) {
                    if (!emailRegex.test(singleEmail)) {
                        alert('Please provide valid email address(es). Invalid email: ' + singleEmail);
                        return;
                    }
                }
                if (emails.length === 0) {
                    alert('Please provide at least one valid email address.');
                    return;
                }

                const params = new URLSearchParams();
                params.append('email', email);
                params.append('range', range);
                // Append chosen status filters
                const hostStatuses = Array.from(document.querySelectorAll('#sr-host-statuses input:checked')).map(cb=>cb.value).join(',');
                const svcStatuses  = Array.from(document.querySelectorAll('#sr-svc-statuses input:checked')).map(cb=>cb.value).join(',');
                params.append('hostStatuses', hostStatuses);
                params.append('svcStatuses', svcStatuses);
                params.append('saveToServer', document.getElementById('sr-save-to-server').checked);

                const sendNowBtn = document.getElementById('sr-send-now');
                sendNowBtn.disabled = true;
                sendNowBtn.textContent = 'Sending...';

                fetch('functions/reportsFunctions/sendInstantReport.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: params.toString()
                }).then(r=>r.json())
                  .then(js=>{
                      if(js.success){
                          if(srStatus){
                              srStatus.textContent = js.message || 'Report sent successfully';
                              srStatus.classList.remove('not-scheduled');
                              srStatus.classList.add('scheduled');
                          }
                          // Keep modal open so user sees status
                      } else {
                          if(srStatus){
                              srStatus.textContent = js.message || 'Failed to send report';
                              srStatus.classList.remove('scheduled');
                              srStatus.classList.add('not-scheduled');
                          }
                      }
                  })
                  .catch(err=>{
                      console.error('Send now error', err);
                      if(srStatus){
                          srStatus.textContent = 'Error sending report';
                          srStatus.classList.remove('scheduled');
                          srStatus.classList.add('not-scheduled');
                      }
                  })
                  .finally(()=>{
                      sendNowBtn.disabled = false;
                      sendNowBtn.textContent = 'Send Now';
                  });
            });
        }
        if(srForm){
            srForm.addEventListener('submit', function(e){
                e.preventDefault();
                const email = document.getElementById('sr-email').value.trim();
                const freq  = document.getElementById('sr-frequency').value;
                const time  = document.getElementById('sr-time').value || '00:05';
                const range = parseInt(document.getElementById('sr-range').value,10) || 1;

                // Validate multiple emails separated by semicolons
                const emails = email.split(';').map(e => e.trim()).filter(e => e.length > 0);
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                for (const singleEmail of emails) {
                    if (!emailRegex.test(singleEmail)) {
                        alert('Please provide valid email address(es). Invalid email: ' + singleEmail);
                        return;
                    }
                }
                if (emails.length === 0) {
                    alert('Please provide at least one valid email address.');
                    return;
                }

                const params = new URLSearchParams();
                params.append('email', email);
                params.append('frequency', freq);
                params.append('time', time);
                params.append('range', range);
                // Append chosen status filters
                const hostStatuses = Array.from(document.querySelectorAll('#sr-host-statuses input:checked')).map(cb=>cb.value).join(',');
                const svcStatuses  = Array.from(document.querySelectorAll('#sr-svc-statuses input:checked')).map(cb=>cb.value).join(',');
                params.append('hostStatuses', hostStatuses);
                params.append('svcStatuses', svcStatuses);
                params.append('saveToServer', document.getElementById('sr-save-to-server').checked);

                const saveBtn = document.getElementById('sr-save');
                saveBtn.disabled = true;
                saveBtn.textContent = 'Saving...';

                fetch('functions/reportsFunctions/addReportCron.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: params.toString()
                }).then(r=>r.json())
                  .then(js=>{
                      if(js.success){
                          if(srStatus){
                              srStatus.textContent = 'Report scheduled successfully';
                              srStatus.classList.remove('not-scheduled');
                              srStatus.classList.add('scheduled');
                          }
                          // Keep modal open so user sees status
                      } else {
                          if(srStatus){
                              srStatus.textContent = js.message || 'Failed to schedule report';
                              srStatus.classList.remove('scheduled');
                              srStatus.classList.add('not-scheduled');
                          }
                      }
                  })
                  .catch(err=>{
                      console.error('Schedule error', err);
                      alert('Error scheduling report');
                  })
                  .finally(()=>{
                      saveBtn.disabled = false;
                      saveBtn.textContent = 'Save';
                  });
            });
        }
        // Disable scheduled report handler
        if(srDisableBtn){
            srDisableBtn.addEventListener('click', ()=>{
                srDisableBtn.disabled = true;
                srDisableBtn.textContent = 'Disabling...';
                fetch('functions/reportsFunctions/deleteReportCron.php', { method: 'POST' })
                    .then(r=>r.json())
                    .then(js=>{
                        if(js.success){
                            srDisableBtn.style.display = 'none';
                            if(srStatus){
                                srStatus.textContent = 'No report scheduled';
                                srStatus.classList.remove('scheduled');
                                srStatus.classList.add('not-scheduled');
                            }
                        } else {
                            if(srStatus){
                                srStatus.textContent = js.message || 'Failed to disable scheduled report';
                                srStatus.classList.remove('scheduled');
                                srStatus.classList.add('not-scheduled');
                            }
                        }
                    })
                    .catch(err=>{
                        console.error('Disable schedule error', err);
                        if(srStatus){
                            srStatus.textContent = 'Error disabling schedule';
                            srStatus.classList.remove('scheduled');
                            srStatus.classList.add('not-scheduled');
                        }
                    })
                    .finally(()=>{
                        srDisableBtn.disabled = false;
                        srDisableBtn.textContent = 'Disable';
                    });
            });
        }
    });
})();
